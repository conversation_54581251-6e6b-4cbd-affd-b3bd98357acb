{"name": "videoeditor", "version": "3.1.0", "description": "Web Based Video Editor Using MLT Framework", "main": "index.js", "scripts": {"start": "babel-node server.js", "build": "./node_modules/.bin/webpack --mode production", "dev-start": "nodemon --exec babel-node server.js --ignore public/", "dev-build": "./node_modules/.bin/webpack -wd", "eslint": "npx eslint controllers/ models/ react/ test/ config.js router.js server.js", "test": "mocha --parallel"}, "repository": {"type": "git", "url": "git+https://github.com/kudlav/videoeditor"}, "author": "xkudla15", "license": "Apache-2.0", "bugs": {"url": "https://github.com/kudlav/videoeditor/issues"}, "homepage": "https://github.com/kudlav/videoeditor#readme", "engines": {"node": ">= 10.13.0"}, "dependencies": {"@babel/core": "^7.28.0", "@babel/node": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "babel-loader": "^10.0.0", "body-parser": "^2.2.0", "busboy": "^1.6.0", "css-loader": "^7.1.2", "ejs": "^3.1.10", "express": "^5.1.0", "extract-loader": "^5.1.0", "file-loader": "^6.2.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.3", "jsdom": "^26.1.0", "log4js": "^6.9.1", "nanoid": "^5.1.5", "node-sass": "^9.0.0", "nodemailer": "^7.0.5", "postcss-loader": "^8.1.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone-uploader": "^2.11.0", "react-modal": "^3.16.3", "rwlock": "^5.0.0", "sass-loader": "^16.0.5", "vis-timeline": "^8.1.0", "webpack": "^5.100.2", "webpack-cli": "^6.0.1"}, "devDependencies": {"@babel/cli": "^7.28.0", "babel-eslint": "^10.1.0", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "mocha": "^11.7.1", "node-fetch": "^3.3.2", "nodemon": "^3.1.10"}}