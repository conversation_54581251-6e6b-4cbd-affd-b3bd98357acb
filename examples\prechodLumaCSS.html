<!DOCTYPE html>
<html lang="cs" dir="ltr">
<!--
 @file Example with luma transition using CSS
 <AUTHOR> <<EMAIL>>
-->
<head>
	<meta charset="UTF-8">
	<title>Online Videoeditor</title>
	<style type="text/css">
		.transition-luma {
			transition-property: opacity;
			transition-timing-function: linear;
		}
	</style>
</head>
<body>
	<h1>Přechod mezi videi - prolnutí pomocí CSS animace</h1>
	<strong>Problém při pozastavení videa běží animace dál. Ošetření by spočívalo v uložení aktuálního stavu animace a poté animaci navázat. Problém rovněž nastane, pokud by se přehrávání videa začalo zpoždovat, nebo pokud by se čekalo na vyrovnávací paměť.</strong>
	<div style="display: block; position: relative; background-color: #000; width: 480px; height: 240px;">
		<video style="position: absolute; top: 0; left: 0; z-index: 2; width: 480px; height: 240px; background: #000;" id="video1" class="transition-luma"><source type="video/mp4" src="video1.mp4"></video>
		<video style="position: absolute; top: 0; left: 0; z-index: 1; width: 480px; height: 240px; background: #000;" id="video2" class="transition-luma"><source type="video/mp4" src="video2.mp4"></video>
	</div>
	<br>
		<div class="prev-toolbar">
			<button id="prev-reset" class="no-border" title="Zastavit přehrávání">reset</button>
			<button id="prev-play" title="Pokračovat v přehrávání">přehrát</button>
			<button id="prev-pause" title="Pozastavit přehrávání">pozastavit</button>
		</div>
	<script>

		const video1 = document.getElementById('video1');
		const video2 = document.getElementById('video2');
		const reset = document.getElementById('prev-reset');
		const play = document.getElementById('prev-play');
		const pause = document.getElementById('prev-pause');
		let end = false;
		let transition = false;
		
		let transitionDuration = 2;

		let videoMain = video1;
		video1.addEventListener('timeupdate', onTimeUpdate, false)
		function onTimeUpdate() {
			if (!transition && (video1.currentTime >= (video1.duration - transitionDuration))) {
				video1.style.transitionDuration = transitionDuration + 's';
				video1.style.opacity = 0;
				video2.play();
				transition = true;
			}
		}

		video1.addEventListener('ended', videoSwap, false);
		function videoSwap() {
			videoMain = video2;
			resetTransition(video1);
			video1.style.zIndex = 1;
			video2.style.zIndex = 2;
			togglePlay();
		}

		video2.addEventListener('ended', timelineEnd, false);
		function timelineEnd() {
			end = true;
		}

		reset.addEventListener('click', toggleReset, false);
		play.addEventListener('click', togglePlay, false);
		pause.addEventListener('click', togglePause, false);

		function toggleReset() {
			video1.pause();
			video2.pause();
			resetTransition(video1);
			video1.currentTime = 0;
			video2.currentTime = 0;
			video1.style.zIndex = 2;
			video2.style.zIndex = 1;
			videoMain = video1;
		}

		function togglePlay() {
			if (end) {
				toggleReset();
				end = false;
			}
			videoMain.play();
		}

		function togglePause() {
			video1.pause();
			video2.pause();
		}

		function resetTransition(element) {
			element.style.transitionDuration = '0s';
			element.style.opacity = 1;
			transition = false;
		}

	</script>
</body>
</html>
