<!DOCTYPE html>
<html lang="cs" dir="ltr">
<!--
 @file Example for playlist
 <AUTHOR> <<EMAIL>>
-->
<head>
	<meta charset="UTF-8">
	<title>Online Videoeditor</title>
</head>
<body>
	<h1>Posloupnost videí</h1>
	<div style="display: block; position: relative; background-color: #000; width: 480px; height: 240px;">
		<video style="position: absolute; top: 0; left: 0; z-index: 2; width: 480px; height: 240px; background: #000;" id="video1"><source type="video/mp4" src="video1.mp4"></video>
		<video style="position: absolute; top: 0; left: 0; z-index: 1; width: 480px; height: 240px; background: #000;" id="video2"><source type="video/mp4" src="video2.mp4"></video>
	</div>
	<br>
		<div class="prev-toolbar">
			<button id="prev-reset" class="no-border" title="Zastavit přehrávání">reset</button>
			<button id="prev-play" title="Pokračovat v přehrávání">přehrát</button>
			<button id="prev-pause" title="Pozastavit přehrávání">pozastavit</button>
		</div>
	<script>

		const video1 = document.getElementById('video1');
		const video2 = document.getElementById('video2');
		const reset = document.getElementById('prev-reset');
		const play = document.getElementById('prev-play');
		const pause = document.getElementById('prev-pause');
		let end = false;

		let videoMain = video1;
		video1.addEventListener('ended', videoSwap, false);
		function videoSwap() {
			videoMain = video2;
			video1.style.zIndex = 1;
			video2.style.zIndex = 2;
			togglePlay();
		}

		video2.addEventListener('ended', timelineEnd, false);
		function timelineEnd() {
			end = true;
		}

		reset.addEventListener('click', toggleReset, false);
		play.addEventListener('click', togglePlay, false);
		pause.addEventListener('click', togglePause, false);

		function toggleReset() {
			videoMain.pause();
			video1.currentTime = 0;
			video2.currentTime = 0;
			video1.style.zIndex = 2;
			video2.style.zIndex = 1;
			videoMain = video1;
		}

		function togglePlay() {
			if (end) {
				toggleReset();
				end = false;
			}
			videoMain.play();
		}

		function togglePause() {
			videoMain.pause();
		}

	</script>
</body>
</html>
