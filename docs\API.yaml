openapi: 3.0.1
info:
  title: Videoeditor
  description: API for clients of Web Based Video Editor.
  termsOfService: 'https://github.com/kudlav/videoeditor/blob/master/LICENSE'
  contact:
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: 'http://www.apache.org/licenses/LICENSE-2.0.html'
  version: 3.1.0
externalDocs:
  description: Github repository
  url: 'https://github.com/kudlav/videoeditor/'
servers:
  - url: 'https://editor.prednasky.com/api'
tags:
  - name: project
    description: Everything about project
paths:
  /project:
    post:
      tags:
        - project
      summary: Start new project
      operationId: projectPOST
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  project:
                    type: string
                    description: ID of new project.
  '/project/{projectID}':
    get:
      tags:
        - project
      summary: Get state of project
      operationId: projectGET
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Project loaded.
          content:
            application/json:
              schema:
                type: object
                properties:
                  project:
                    type: string
                    description: id of project
                  resources:
                    type: object
                    properties:
                      _ID_:
                        type: object
                        properties:
                          id:
                            type: string
                          duration:
                            type: string
                            nullable: true
                          mime:
                            type: string
                            nullable: true
                          name:
                            type: string
                            nullable: true
                    description: associtaive array of resource objects
                  timeline:
                    type: object
                    properties:
                      audio:
                        type: array
                        items:
                          $ref: '#/components/schemas/Timeline'
                      video:
                        type: array
                        items:
                          $ref: '#/components/schemas/Timeline'
                    description: audio and video tracks with items
                  processing:
                    type: number
                    nullable: true
                    description: percentage progress when processing final video, null any other time
        '404':
          description: Project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - project
      summary: NOT IMPLEMENTED! Remove project and all files
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      deprecated: true
      responses:
        '200':
          description: Project removed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - project
      summary: 'Finish project, get video'
      operationId: projectPUT
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                email:
                  type: string
                  description: email for finish notification
      responses:
        '200':
          description: Rendering has started.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/file':
    post:
      tags:
        - file
      summary: Upload new file
      operationId: projectFilePOST
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  description: file to upload
                  format: binary
        required: true
      responses:
        '200':
          description: File uploaded.
          content:
            application/json:
              schema:
                type: object
                properties:
                  msg:
                    type: string
                    description: info message
                  resource_id:
                    type: string
                    description: uploaded file ID
        '400':
          description: 'Bad request, missing file in request body.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/file/{fileID}':
    delete:
      tags:
        - file
      summary: Remove file from project
      operationId: projectFileDELETE
      parameters:
        - name: projectID
          in: path
          description: project ID
          required: true
          schema:
            type: string
        - name: fileID
          in: path
          description: file ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: File removed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '403':
          description: 'Unable to remove, resource is in use.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Resource or project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - file
      summary: Add file to the end of timeline
      operationId: projectFilePUT
      parameters:
        - name: projectID
          in: path
          description: project ID
          required: true
          schema:
            type: string
        - name: fileID
          in: path
          description: file ID
          required: true
          schema:
            type: string
        - name: track
          in: header
          description: track ID
          required: true
          schema:
            type: string
        - name: duration
          in: header
          description: 'required for image files, format: "00:00:00,000"'
          schema:
            type: string
      responses:
        '200':
          description: File added to a timeline.
          content:
            application/json:
              schema:
                type: object
                properties:
                  msg:
                    type: string
                    description: info message
                  timeline:
                    type: string
                    description: file added to timeline with following ID
        '400':
          description: 'Bad request, missing duration of image.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Unsupported file type for the timeline.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Resource or project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/filter':
    post:
      tags:
        - filter
      summary: Apply filter to a timeline item
      operationId: projectFilterPOST
      description: >-
        You can use filters from MLT documentation. Each filter can be applied
        only once. There are some special names to distinguish multiple
        brightness and volume filters: fadeInBrightnerss => brightness,
        fadeOutBrightnerss => brightness, fadeInVolume => volume, fadeOutVolume
        => volume
      externalDocs:
        url: 'https://www.mltframework.org/plugins/PluginsFilters/'
        description: Available filters and usage
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                track:
                  type: string
                  description: track containing items
                item:
                  type: number
                  description: index of track item
                filter:
                  type: string
                  description: type of filter
                params:
                  type: object
                  description: filter properties
        required: true
      responses:
        '200':
          description: Filter applied.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 'Bad request, missing track, item or filter parameter.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Filter already applied.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 'Track, item or project not found.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - filter
      summary: Remove filter from a timeline item
      operationId: projectFilterDELETE
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                track:
                  type: string
                  description: track containing items
                item:
                  type: number
                  description: index of track item
                filter:
                  type: string
                  description: type of filter
        required: true
      responses:
        '200':
          description: Filter removed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 'Bad request, missing track, item or filter parameter.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 'Track, item, filter or project not found.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/transition':
    post:
      tags:
        - transition
      summary: Apply transition between two timeline items
      operationId: projectTransitionPOST
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                track:
                  type: string
                  description: track containing items
                itemA:
                  type: string
                  description: first item of transition
                itemB:
                  type: string
                  description: second item of transition
                transition:
                  type: string
                  description: type of transition
                duration:
                  type: string
                  description: duration of transition
        required: true
      responses:
        '200':
          description: Transition applied.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 'Bad request, missing or invalid parameters in request body.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Transition already exists.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 'Item, track or project not found.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - transition
      summary: NOT IMPLEMENTED! Remove transition between two timeline items
      parameters:
        - name: projectID
          in: path
          description: ID of project.
          required: true
          schema:
            type: string
      deprecated: true
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                track:
                  type: string
                  description: track containing items
                itemA:
                  type: string
                  description: first item of transition
                itemB:
                  type: string
                  description: second item of transition
                transition:
                  type: string
                  description: type of transition
      responses:
        '200':
          description: Transition removed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 'Bad request, missing track, item or transition parameter.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 'Track, item, transition or project not found.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/item':
    delete:
      tags:
        - item
      summary: Remove item from timeline
      operationId: projectItemDELETE
      parameters:
        - name: projectID
          in: path
          description: project ID
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                track:
                  type: string
                  description: track containing items
                item:
                  type: number
                  description: index of track item
        required: true
      responses:
        '200':
          description: Item removed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 'Bad request, missing track or item parameter.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Track or item not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/item/move':
    put:
      tags:
        - item
      summary: Move item in timeline
      operationId: projectItemPUTmove
      parameters:
        - name: projectID
          in: path
          description: project ID
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                track:
                  type: string
                  description: track containing item
                trackTarget:
                  type: string
                  description: destination track
                item:
                  type: number
                  description: index of track item
                time:
                  type: string
                  description: 'new start time of item, in format ''00:00:00,000'''
        required: true
      responses:
        '200':
          description: Item moved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 'Bad request, missing track or item parameter.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Not enought free space at target place.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Track, item or project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/item/split':
    put:
      tags:
        - item
      summary: Split item in timeline into two parts
      operationId: projectItemPUTsplit
      parameters:
        - name: projectID
          in: path
          description: project ID
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                track:
                  type: string
                  description: track containing items
                item:
                  type: number
                  description: index of track item
                time:
                  type: string
                  description: 'relative position of cut in format ''00:00:00,000'''
        required: true
      responses:
        '200':
          description: Item splitted.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: 'Bad request, missing track or item parameter or time longer than item duration.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Track or item not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/track':
    post:
      tags:
        - track
      summary: Add track
      operationId: projectTrackPOST
      parameters:
        - name: projectID
          in: path
          description: project ID
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                type:
                  type: string
                  description: Type of track (video/audio).
        required: true
      responses:
        '200':
          description: Track created.
          content:
            application/json:
              schema:
                type: object
                properties:
                  msg:
                    type: string
                    description: Info message.
                  track:
                    type: string
                    description: ID of new track.
        '400':
          description: 'Bad request, missing type parameter (video/audio).'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/project/{projectID}/track/{trackID}':
    delete:
      tags:
        - track
      summary: Remove track. When removing default track, track with lowest track became as default.
      operationId: projectTrackDELETE
      parameters:
        - name: projectID
          in: path
          description: project ID
          required: true
          schema:
            type: string
        - name: trackID
          in: path
          description: track ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Track removed.
          content:
            application/json:
              schema:
                type: object
                properties:
                  msg:
                    type: string
                    description: Info message.
        '400':
          description: 'Bad request, missing track parameter.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 'Unable to remove default track.'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Project not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    ErrorResponse:
      type: object
      properties:
        err:
          type: string
          description: error message title
        msg:
          type: string
          description: error description
    SuccessResponse:
      type: object
      properties:
        msg:
          type: string
          description: info message
    Timeline:
      type: object
      properties:
        id:
          type: string
        duration:
          type: string
        items:
          type: array
          items:
            type: object
            properties:
              resource:
                type: string
              in:
                type: string
              out:
                type: string
              start:
                type: string
              end:
                type: string
              filters:
                type: array
                items:
                  type: object
                  properties:
                    service:
                      type: string
