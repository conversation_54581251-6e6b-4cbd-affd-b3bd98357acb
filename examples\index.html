<!DOCTYPE html>
<html lang="cs" dir="ltr">
<!--
 @file Index file of examples
 <AUTHOR> <<EMAIL>>
-->
<head>
	<meta charset="UTF-8">
	<title>Online Videoeditor</title>
</head>
<body>
	<h1>Úprava videí na straně prohlížeče</h1>
	<h2>Řešení pomocí HTML5 médií a SVG</h2>
	Hlavní výhoda - filtr lze aplikovat jen nastavením třídy elementu videa.
	<ol>
		<li><a href="ovladani.html">Ov<PERSON><PERSON><PERSON><PERSON><PERSON>, pr<PERSON>ce s časem</a></li>
		<li><a href="playlist.html">Posloupnost videí (playlist)</a></li>
		<li><a href="prechodLuma.html">Přechod mezi videi - prolnutí</a></li>
		<li><a href="prechodLuma.html">Přechod mezi videi - možné efekty/obrazce</a> 🔗externí odkaz</li>
		<li><a href="http://html5videoguide.net/DefinitiveGuide/code_c4_6.html">Filtr zvuku - ztišit</a> 🔗externí odkaz</li>
		<li><a href="http://html5videoguide.net/DefinitiveGuide/code_c4_5.html">Filtr zvuku - zeslabit/<strike>zesílit</strike></a> 🔗externí odkaz</li>
		<li><a href="http://html5videoguide.net/DefinitiveGuide/code_c5_10.html">Obrazové filtry - matice barev, přenos komponent, konvoluční matice, substituční mapa, matice barev</a> 🔗externí odkaz</li>
		<li><a href="http://html5videoguide.net/DefinitiveGuide/code_c5_11.html">Obrazové filtry - aplikování více filtrů současně</a> 🔗externí odkaz</li>
	</ol>
	<h2>Řešení pomocí HTML5 médií a Canvas</h2>
	Naslouchání události <i>timeupdate</i> - událost je vyvolána každých 15 až 250 ms (FF 100-250 ms). Nepoužitelné. Video je příliš trhané a celkový dojem z videa je špatný.<br>
	Vykreslování pomocí set timer s časem 0 - ok, funguje dobře. Výhoda - funguje i v Microsoft Edge. 
</body>
</html>
