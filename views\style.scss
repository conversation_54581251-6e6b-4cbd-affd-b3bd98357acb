/**
 * @file SASS file with all CSS rules
 * <AUTHOR> <<EMAIL>>; <PERSON> (MIT License)
 */

@font-face {
	font-family: SourceSans;
	src: url('/fonts/sans/SourceSansPro-Regular.ttf');
}

body {
	font-family: SourceSans, sans-serif;
	background: #1F1F1F;
	color: white;
	font-size: 14px;
	margin: 0;
}

header {
	padding: 0 10px;
	flex: 0 1 auto;
	display: flex;
	flex-wrap: nowrap;
	align-items: center;

	a {
		margin: 0 1em;
	}

	.right {
		margin-left: auto;
	}
}

main {
	flex: 1 1 auto;

	> div {
		display: flex;
		flex-direction: row;
		align-content: stretch;
		padding: 5px;
	}
}

footer {
	width: 100%;
	padding: 5px;
	box-sizing: border-box;
	flex: 0 1 auto;
}

footer, header {
	box-shadow: 0 0 10px #111;
	background: #373737;
}

h3 {
	margin: 5px;
	font-family: SourceSans;
	font-weight: inherit;
}

h2.logo, h2.error {
	img {
		width: 120px;
		height: 120px;
		vertical-align: middle;
		padding-right: 1em;
	}
}

h2.logo {
	font-size: 4em;
}

select, input {
	height: 28px;
	background: #25282d;
	color: #fff;
	border: 1px solid #555;
	border-radius: 5px;
}

select option {
	margin: 40px;
	background: #25282d;
	color: #fff;
}

a {
	color: inherit;
}

progress {
	margin: 0 1em;
}

#app {
	display: flex;
	flex-direction: column;
	align-items: stretch;
	min-height: 100vh;
}

#sources, #preview {
	flex: 1;
}

#preview {
	width: 490px;
	text-align: center;

	video {
		width: 480px;
		height: 240px;
		background: #000;
	}
}

#time {
	display: inline-block;
	float: right;
	margin-right: 1rem;
	font-size: 22px;
}

.modal {
	position: absolute;
	top: 35%;
	left: 50%;
	right: auto;
	bottom: auto;
	margin-right: -50%;
	transform: translate(-50%, -50%);
	background-color: #373737;
	border: 2px solid #555;
	border-radius: 4px;
	min-width: 40%;
	outline: none;

	> h2 {
		padding: 15px 20px;
		margin: 0;
		border-bottom: 1px solid #555;
	}

	> h3 {
		padding: 5px 20px;
		margin: 0;
	}

	> div {
		padding: 20px;
		text-align: center;
	}

	table {
		width: 100%;
		margin: 0 auto;
		table-layout: initial;
	}
}

.overlay{
	position: fixed;
	top: 0px;
	left: 0px;
	right: 0px;
	bottom: 0px;
	background-color: rgba(0, 0, 0, 0.79);
	z-index: 100;
}

.material-icons {
	font-size: 18px;
	padding-right: 3px;
	position: relative;
	top: 3px;
}

.icon-huge {
	font-size: 4em;
}

.prev-toolbar {
	border: 1px solid #2b2c2f;
	display: inline-block;
	margin-top: 10px;
	padding: 0 4px 0 4px;
	border-radius: 5px;
	background: #25282d;
}

.no-border {
	border: 0 !important;
}

.divider {
	display: inline-block;
	margin: 0 10px;
	border-left: 1px solid #888;
	height: 1.4em;
}

.loader {
	border: 16px solid transparent;
	border-radius: 50%;
	border-top: 16px solid #888;
	border-bottom: 16px solid #888;
	width: 120px;
	height: 120px;
	animation: spin 3s linear infinite;
	display: inline-block;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/*
 * BUTTONS
 */
button, input[type="submit"] {
	font-size: 14px;
	color: inherit;
	border: 1px solid #555;
	display: inline-block;
	padding: 2px 7px 4px 7px;
	margin: 8px 3px;
	cursor: pointer;
	font-family: SourceSans;
	border-radius: 5px;
	background: #25282d;
	min-height: 1.85em;

	&:hover {
		background-color: #161c21;
	}
	&:disabled {
		color: #555;
		background-color: #25282d;
		cursor: not-allowed;
	}
	&.success {
		border-color: #336620;
	}
	&.error {
		border-color: #7a281b;
	}
	&.success:hover {
		background-color: #123027;
	}
	&.error:hover {
		background-color: #301212;
	}

	.prev-toolbar & {
		padding: 4px;
		border: none;
		border-left: 1px solid #555;
		border-radius: 0;
		margin: 0;
	}
}

input[type="submit"] {
	height: 1.85em;
}

/*
 * TABLES
 */
table {
	table-layout: fixed;
	width: 100%;
	background: #292929;
	word-break: break-all;
	border-collapse: collapse;

	button {
		border: none;
		margin: 0;
	}
}

th, td {
	vertical-align: middle;
	font-size: 15px;
	line-height: 24px;
	padding: 7px;
}

tr:nth-child(even) {
	background: #2b2c2e;
}

.column-right {
	text-align: right;
}

#sources {
	.preview {
		height: 50px;
		text-align: center;
		font-size: 3.5em;
	}

	td:first-child {
		width: 2.5em;
	}

	table i {
		font-size: 2em;
	}
}

/*
 * DROPZONE
 *
 * The MIT License (MIT)
 * Copyright (c) 2018 Kyle Bebak
 */
.dzu-dropzone {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	min-height: 120px;
	margin: 0 auto;
	position: relative;
	box-sizing: border-box;
	transition: all .15s linear;
	border: 2px solid #555;
	border-radius: 4px;
}

.dzu-dropzoneActive {
	background-color: #777;
}

.dzu-dropzoneDisabled {
	opacity: 0.5;
}

.dzu-dropzoneDisabled *:hover {
	cursor: unset;
}

.dzu-input {
	display: none;
}

.dzu-inputLabel {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	font-size: 20px;
	cursor: pointer;
}

.dzu-inputLabelWithFiles {
	display: flex;
	justify-content: center;
	align-items: center;
	align-self: flex-start;
	padding: 0 14px;
	min-height: 32px;
	background-color: #25282d;
	border: 1px solid #555;
	border-radius: 4px;
	font-size: 14px;
	margin: 20px 0 20px 3%;
	cursor: pointer;
}

.dzu-previewContainer {
	padding: 20px 3%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	position: relative;
	width: 100%;
	min-height: 60px;
	z-index: 1;
	border-bottom: 1px solid #555;
	box-sizing: border-box;
}

.dzu-previewStatusContainer {
	display: flex;
	align-items: center;
}

.dzu-previewImage {
	width: auto;
	max-height: 50px;
	max-width: 60px;
}

.dzu-previewButton {
	background-size: 14px 14px;
	background-position: center;
	background-repeat: no-repeat;
	width: 14px;
	height: 14px;
	cursor: pointer;
	opacity: 0.9;
	margin: 0 0 2px 10px;
	filter: invert(1);
}

/*
 * TIMELINE
 */
#timeline {
	.vis-timeline {
		border: none;
	}

	.vis-item {
		height: 4em;

		&.video, &.audio {
			border-radius: 10px;
			color: #FFF;
			padding: 2px;
		}

		&.video {
			background-color: #27438b;
			border-color: #1d346e;

			&.stick-right {
				border-right: 5px solid #4caf50;
			}

			&.stick-left {
				border-left: 5px solid #4caf50;
			}
		}

		&.audio {
			background-color: #2d7741;
			border-color: #1f512c;

			&.stick-right {
				border-right: 5px solid #30acff;
			}

			&.stick-left {
				border-left: 5px solid #30acff;
			}
		}

		&.vis-selected {
			border-color: #FFC200;
		}

		div.filter {
			position: relative;
			top: -5px;
			left: -5px;
			width: 0;
			height: 0;
			border-top: 40px solid rgba(255, 255, 255, 0.2);
			border-right: 40px solid transparent;
			z-index: 1;
			display: inline-block;
			vertical-align: top;
		}

		i.filter {
			position: absolute;
			top: 5px;
			left: 4px;
			z-index: 2;
		}
	}

	.vis-time-axis .vis-text {
		color: inherit;
	}

	.vis-time-axis .vis-grid.vis-minor, .vis-time-axis .vis-grid.vis-major, .vis-panel.vis-center, .vis-panel.vis-left, .vis-panel.vis-right, .vis-panel.vis-top, .vis-panel.vis-bottom {
		border-color: #555;
	}

	.vis-custom-time {
		background-color: #FF7676;
	}

	.vis-labelset .vis-label .vis-inner {
		padding: 0;
	}

	.vis-foreground .vis-group {
		border-bottom: none;
	}
}
